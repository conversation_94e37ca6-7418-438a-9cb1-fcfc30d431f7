package com.jingfang.wh_item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.dto.ItemDto;
import com.jingfang.wh_item.module.dto.ItemRegistrationDto;
import com.jingfang.wh_item.module.dto.ItemStockAdjustmentDto;
import com.jingfang.wh_item.module.request.ItemSearchRequest;
import com.jingfang.wh_item.module.request.ItemStockStatisticsRequest;
import com.jingfang.wh_item.module.request.ItemStockViewRequest;
import com.jingfang.wh_item.module.vo.ItemDetailVo;
import com.jingfang.wh_item.module.vo.ItemStockStatisticsVo;
import com.jingfang.wh_item.module.vo.ItemStockViewVo;
import com.jingfang.wh_item.module.vo.ItemVo;

import java.util.List;

/**
 * 物品管理Service接口
 */
public interface ItemService {
    
    /**
     * 新增物品（包含库存信息，实际为物品单体入库）
     *
     * @param itemDto 物品信息
     * @return 结果
     */
    boolean addItem(ItemDto itemDto);
    
    /**
     * 物品登记（只登记基本信息和属性，不包含库存信息）
     *
     * @param registrationDto 物品登记信息
     * @return 结果
     */
    boolean registerItem(ItemRegistrationDto registrationDto);
    
    /**
     * 修改物品
     *
     * @param itemDto 物品信息
     * @return 结果
     */
    boolean updateItem(ItemDto itemDto);
    
    /**
     * 删除物品
     *
     * @param itemId 物品ID
     * @return 结果
     */
    boolean deleteItem(String itemId);
    
    /**
     * 批量删除物品
     *
     * @param itemIds 需要删除的物品ID
     * @return 结果
     */
    boolean deleteItems(List<String> itemIds);
    
    /**
     * 查询物品列表
     *
     * @param request 查询条件
     * @return 物品列表
     */
    IPage<ItemVo> selectItemList(ItemSearchRequest request);
    
    /**
     * 查询物品详情
     *
     * @param itemId 物品ID
     * @return 物品详情
     */
    ItemDetailVo getItemDetail(String itemId);
    
    /**
     * 更新物品库存
     *
     * @param itemId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 变动数量（正数增加，负数减少）
     * @param operationType 操作类型（1-入库，2-出库，3-盘点）
     * @param operationId 关联单据ID
     * @param remark 备注
     * @param shelfLocation 货架位置
     * @return 结果
     */
    boolean updateItemStock(String itemId, Integer warehouseId, int quantity, int operationType, String operationId, String remark, String shelfLocation);
    
    /**
     * 检查物品是否存在
     *
     * @param itemId 物品ID
     * @return 是否存在
     */
    boolean existsById(String itemId);
    
    /**
     * 物品库存统计查询
     *
     * @param request 查询条件
     * @return 库存统计列表
     */
    IPage<ItemStockStatisticsVo> selectItemStockStatistics(ItemStockStatisticsRequest request);
    
    /**
     * 查询库存不足的物品列表（库存下限告警）
     * 查询总库存低于企业级安全库存的物品
     *
     * @return 库存不足的物品列表
     */
    List<ItemVo> selectLowStockItems();
    
    /**
     * 库存查看（支持按仓库和按物品两种展示模式）
     *
     * @param request 查询条件
     * @return 库存查看列表
     */
    IPage<ItemStockViewVo> selectItemStockView(ItemStockViewRequest request);
    
    /**
     * 调整物品库存数量
     *
     * @param adjustmentDto 库存调整信息
     * @return 结果
     */
    boolean adjustItemStock(ItemStockAdjustmentDto adjustmentDto);
    
    /**
     * 检查库存是否充足
     *
     * @param itemId 物品ID
     * @param warehouseId 仓库ID
     * @param quantity 需要的数量
     * @return 是否充足
     */
    boolean checkStockSufficient(String itemId, Integer warehouseId, int quantity);
} 