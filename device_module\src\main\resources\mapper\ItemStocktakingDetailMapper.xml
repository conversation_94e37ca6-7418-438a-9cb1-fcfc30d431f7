<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jingfang.wh_item.mapper.ItemStocktakingDetailMapper">

    <resultMap type="com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo" id="ItemStocktakingDetailResult">
        <result property="detailId" column="detail_id"/>
        <result property="stocktakingId" column="stocktaking_id"/>
        <result property="itemId" column="item_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="specModel" column="spec_model"/>
        <result property="unit" column="unit"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="shelfLocation" column="shelf_location"/>
        <result property="bookQuantity" column="book_quantity"/>
        <result property="actualQuantity" column="actual_quantity"/>
        <result property="differenceQuantity" column="difference_quantity"/>
        <result property="differenceReason" column="difference_reason"/>
        <result property="checkerName" column="checker_name"/>
        <result property="checkTime" column="check_time"/>
        <result property="status" column="status"/>
        <result property="statusName" column="status_name"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <resultMap type="com.jingfang.wh_item.module.vo.ItemStocktakingVo" id="StocktakingProgressResult">
        <result property="totalItems" column="total_items"/>
        <result property="checkedItems" column="checked_items"/>
        <result property="differenceItems" column="difference_items"/>
        <result property="progress" column="progress"/>
    </resultMap>

    <sql id="selectStocktakingDetailVo">
        select d.detail_id,
               d.stocktaking_id,
               d.item_id,
               i.item_name,
               i.item_code,
               i.spec_model,
               i.unit,
               d.warehouse_id,
               null as warehouse_name,
               d.shelf_location,
               d.book_quantity,
               d.actual_quantity,
               d.difference_quantity,
               d.difference_reason,
               d.checker_name,
               d.check_time,
               d.status,
               case d.status
                   when 0 then '待盘点'
                   when 1 then '已盘点'
                   when 2 then '有差异'
                   else '未知'
               end as status_name,
               d.create_time
        from item_stocktaking_detail d
        left join item_base_info i on d.item_id = i.item_id
    </sql>

    <select id="selectStocktakingDetailList" parameterType="String" resultMap="ItemStocktakingDetailResult">
        <include refid="selectStocktakingDetailVo"/>
        where d.stocktaking_id = #{stocktakingId}
        order by d.create_time asc
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into item_stocktaking_detail (
            detail_id,
            stocktaking_id,
            item_id,
            warehouse_id,
            shelf_location,
            book_quantity,
            status,
            create_time
        ) values
        <foreach collection="details" item="detail" separator=",">
            (
                #{detail.detailId},
                #{detail.stocktakingId},
                #{detail.itemId},
                #{detail.warehouseId},
                #{detail.shelfLocation},
                #{detail.bookQuantity},
                #{detail.status},
                #{detail.createTime}
            )
        </foreach>
    </insert>

    <select id="selectStocktakingProgress" parameterType="String" resultMap="StocktakingProgressResult">
        select 
            count(*) as total_items,
            sum(case when status > 0 then 1 else 0 end) as checked_items,
            sum(case when status = 2 then 1 else 0 end) as difference_items,
            case 
                when count(*) = 0 then 0
                else round(sum(case when status > 0 then 1 else 0 end) * 100.0 / count(*), 2)
            end as progress
        from item_stocktaking_detail
        where stocktaking_id = #{stocktakingId}
    </select>

</mapper> 