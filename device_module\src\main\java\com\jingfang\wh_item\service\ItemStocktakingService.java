package com.jingfang.wh_item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jingfang.wh_item.module.dto.ItemStocktakingDto;
import com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest;
import com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;

import java.math.BigDecimal;
import java.util.List;

public interface ItemStocktakingService {

    /**
     * 创建库存盘点计划
     */
    String createStocktaking(ItemStocktakingDto dto, String username);

    /**
     * 生成盘点明细
     */
    boolean generateStocktakingDetails(String stocktakingId);

    /**
     * 开始盘点
     */
    boolean startStocktaking(String stocktakingId, String username);

    /**
     * 录入盘点结果
     */
    boolean recordStocktakingResult(String detailId, BigDecimal actualQuantity, String differenceReason, String username);

    /**
     * 完成盘点
     */
    boolean completeStocktaking(String stocktakingId, String username);

    /**
     * 审核盘点结果
     */
    boolean auditStocktaking(String stocktakingId, boolean approved, String auditRemark, String username);

    /**
     * 应用盘点差异到库存
     */
    boolean applyStocktakingDifferences(String stocktakingId, String username);
    
    /**
     * 查询盘点列表
     */
    IPage<ItemStocktakingVo> selectStocktakingList(ItemStocktakingSearchRequest request);
    
    /**
     * 获取盘点详情
     */
    ItemStocktakingVo getStocktakingDetail(String stocktakingId);
    
    /**
     * 查询盘点明细列表
     */
    List<ItemStocktakingDetailVo> selectStocktakingDetailList(String stocktakingId);
    
    /**
     * 删除盘点计划
     */
    boolean deleteStocktaking(String stocktakingId);
    
    /**
     * 更新盘点计划
     */
    boolean updateStocktaking(ItemStocktakingDto dto, String username);
}
