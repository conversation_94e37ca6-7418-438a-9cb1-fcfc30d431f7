package com.jingfang.wh_item.module.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 库存盘点明细VO
 */
@Data
public class ItemStocktakingDetailVo implements Serializable {
    
    /**
     * 明细ID
     */
    private String detailId;
    
    /**
     * 盘点单ID
     */
    private String stocktakingId;
    
    /**
     * 物品ID
     */
    private String itemId;
    
    /**
     * 物品名称
     */
    private String itemName;
    
    /**
     * 物品编码
     */
    private String itemCode;
    
    /**
     * 规格型号
     */
    private String specModel;
    
    /**
     * 单位
     */
    private String unit;
    
    /**
     * 仓库ID
     */
    private Integer warehouseId;
    
    /**
     * 仓库名称
     */
    private String warehouseName;
    
    /**
     * 货架位置
     */
    private String shelfLocation;
    
    /**
     * 账面数量
     */
    private BigDecimal bookQuantity;
    
    /**
     * 实盘数量
     */
    private BigDecimal actualQuantity;
    
    /**
     * 差异数量
     */
    private BigDecimal differenceQuantity;
    
    /**
     * 差异原因
     */
    private String differenceReason;
    
    /**
     * 盘点人姓名
     */
    private String checkerName;
    
    /**
     * 盘点时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;
    
    /**
     * 状态(0-待盘点，1-已盘点，2-有差异)
     */
    private Integer status;
    
    /**
     * 状态名称
     */
    private String statusName;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    private static final long serialVersionUID = 1L;
} 