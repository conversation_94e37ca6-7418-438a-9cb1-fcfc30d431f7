package com.jingfang.wh_item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jingfang.common.utils.SecurityUtils;
import com.jingfang.common.utils.uuid.UUID;
import com.jingfang.wh_item.mapper.ItemInventoryMapper;
import com.jingfang.wh_item.mapper.ItemStocktakingDetailMapper;
import com.jingfang.wh_item.mapper.ItemStocktakingMapper;
import com.jingfang.wh_item.module.dto.ItemStocktakingDto;
import com.jingfang.wh_item.module.entity.ItemInventory;
import com.jingfang.wh_item.module.entity.ItemStocktaking;
import com.jingfang.wh_item.module.entity.ItemStocktakingDetail;
import com.jingfang.wh_item.module.request.ItemStocktakingSearchRequest;
import com.jingfang.wh_item.module.vo.ItemStocktakingDetailVo;
import com.jingfang.wh_item.module.vo.ItemStocktakingVo;
import com.jingfang.wh_item.service.ItemService;
import com.jingfang.wh_item.service.ItemStocktakingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 库存盘点服务实现类
 */
@Slf4j
@Service
public class ItemStocktakingServiceImpl implements ItemStocktakingService {
    
    @Resource
    private ItemStocktakingMapper stocktakingMapper;
    
    @Resource
    private ItemStocktakingDetailMapper detailMapper;
    
    @Resource
    private ItemInventoryMapper inventoryMapper;
    
    @Resource
    private ItemService itemService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createStocktaking(ItemStocktakingDto dto, String username) {
        try {
            String stocktakingId = UUID.fastUUID().toString(true);
            Date now = new Date();
            
            // 生成盘点单号
            String stocktakingCode = generateStocktakingCode();
            
            // 创建盘点主表记录
            ItemStocktaking stocktaking = new ItemStocktaking();
            stocktaking.setStocktakingId(stocktakingId);
            stocktaking.setStocktakingCode(stocktakingCode);
            stocktaking.setStocktakingName(dto.getStocktakingName());
            stocktaking.setStocktakingType(dto.getStocktakingType());
            stocktaking.setWarehouseId(dto.getWarehouseId());
            stocktaking.setStatus(0); // 草稿状态
            stocktaking.setPlanStartTime(dto.getPlanStartTime());
            stocktaking.setPlanEndTime(dto.getPlanEndTime());
            stocktaking.setCreatorId(SecurityUtils.getUserId());
            stocktaking.setCreatorName(username);
            stocktaking.setRemark(dto.getRemark());
            stocktaking.setCreateTime(now);
            stocktaking.setCreateBy(username);
            
            stocktakingMapper.insert(stocktaking);
            
            log.info("创建库存盘点计划成功，盘点单ID：{}，盘点单号：{}", stocktakingId, stocktakingCode);
            return stocktakingId;
        } catch (Exception e) {
            log.error("创建库存盘点计划失败", e);
            throw new RuntimeException("创建库存盘点计划失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean generateStocktakingDetails(String stocktakingId) {
        try {
            // 查询盘点主表信息
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 0) {
                throw new RuntimeException("只有草稿状态的盘点计划才能生成明细");
            }
            
            // 删除已存在的明细
            LambdaQueryWrapper<ItemStocktakingDetail> deleteWrapper = new LambdaQueryWrapper<>();
            deleteWrapper.eq(ItemStocktakingDetail::getStocktakingId, stocktakingId);
            detailMapper.delete(deleteWrapper);
            
            // 根据盘点类型查询库存数据
            LambdaQueryWrapper<ItemInventory> queryWrapper = new LambdaQueryWrapper<>();
            
            // 如果指定了仓库，则只查询该仓库的库存
            if (stocktaking.getWarehouseId() != null) {
                queryWrapper.eq(ItemInventory::getWarehouseId, stocktaking.getWarehouseId());
            }
            
            // 只查询有库存的记录
            queryWrapper.gt(ItemInventory::getCurrentQuantity, BigDecimal.ZERO);
            
            List<ItemInventory> inventoryList = inventoryMapper.selectList(queryWrapper);
            
            if (inventoryList.isEmpty()) {
                log.warn("未找到需要盘点的库存数据，盘点单ID：{}", stocktakingId);
                return true;
            }
            
            // 生成盘点明细
            List<ItemStocktakingDetail> details = new ArrayList<>();
            Date now = new Date();
            
            for (ItemInventory inventory : inventoryList) {
                ItemStocktakingDetail detail = new ItemStocktakingDetail();
                detail.setDetailId(UUID.fastUUID().toString(true));
                detail.setStocktakingId(stocktakingId);
                detail.setItemId(inventory.getItemId());
                detail.setWarehouseId(inventory.getWarehouseId());
                detail.setShelfLocation(inventory.getShelfLocation());
                detail.setBookQuantity(inventory.getCurrentQuantity());
                detail.setStatus(0); // 待盘点
                detail.setCreateTime(now);
                
                details.add(detail);
            }
            
            // 批量插入明细
            if (!details.isEmpty()) {
                detailMapper.batchInsert(details);
            }
            
            log.info("生成盘点明细成功，盘点单ID：{}，明细数量：{}", stocktakingId, details.size());
            return true;
        } catch (Exception e) {
            log.error("生成盘点明细失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("生成盘点明细失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean startStocktaking(String stocktakingId, String username) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 0) {
                throw new RuntimeException("只有草稿状态的盘点计划才能开始盘点");
            }
            
            // 更新状态为进行中
            stocktaking.setStatus(1);
            stocktaking.setActualStartTime(new Date());
            stocktaking.setUpdateTime(new Date());
            stocktaking.setUpdateBy(username);
            
            stocktakingMapper.updateById(stocktaking);
            
            log.info("开始盘点成功，盘点单ID：{}", stocktakingId);
            return true;
        } catch (Exception e) {
            log.error("开始盘点失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("开始盘点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordStocktakingResult(String detailId, BigDecimal actualQuantity, String differenceReason, String username) {
        try {
            ItemStocktakingDetail detail = detailMapper.selectById(detailId);
            if (detail == null) {
                throw new RuntimeException("盘点明细不存在");
            }
            
            // 计算差异数量
            BigDecimal differenceQuantity = actualQuantity.subtract(detail.getBookQuantity());
            
            // 更新明细信息
            detail.setActualQuantity(actualQuantity);
            detail.setDifferenceQuantity(differenceQuantity);
            detail.setDifferenceReason(differenceReason);
            detail.setCheckerId(SecurityUtils.getUserId());
            detail.setCheckerName(username);
            detail.setCheckTime(new Date());
            
            // 设置状态
            if (differenceQuantity.compareTo(BigDecimal.ZERO) == 0) {
                detail.setStatus(1); // 已盘点，无差异
            } else {
                detail.setStatus(2); // 已盘点，有差异
            }
            
            detailMapper.updateById(detail);
            
            log.info("录入盘点结果成功，明细ID：{}，账面数量：{}，实盘数量：{}，差异数量：{}", 
                    detailId, detail.getBookQuantity(), actualQuantity, differenceQuantity);
            return true;
        } catch (Exception e) {
            log.error("录入盘点结果失败，明细ID：{}", detailId, e);
            throw new RuntimeException("录入盘点结果失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeStocktaking(String stocktakingId, String username) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 1) {
                throw new RuntimeException("只有进行中的盘点计划才能完成");
            }
            
            // 检查是否所有明细都已盘点
            LambdaQueryWrapper<ItemStocktakingDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ItemStocktakingDetail::getStocktakingId, stocktakingId);
            queryWrapper.eq(ItemStocktakingDetail::getStatus, 0); // 待盘点状态
            
            long unfinishedCount = detailMapper.selectCount(queryWrapper);
            if (unfinishedCount > 0) {
                throw new RuntimeException("还有 " + unfinishedCount + " 个物品未完成盘点，无法完成盘点");
            }
            
            // 更新状态为已完成
            stocktaking.setStatus(2);
            stocktaking.setActualEndTime(new Date());
            stocktaking.setUpdateTime(new Date());
            stocktaking.setUpdateBy(username);
            
            stocktakingMapper.updateById(stocktaking);
            
            log.info("完成盘点成功，盘点单ID：{}", stocktakingId);
            return true;
        } catch (Exception e) {
            log.error("完成盘点失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("完成盘点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditStocktaking(String stocktakingId, boolean approved, String auditRemark, String username) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 2) {
                throw new RuntimeException("只有已完成的盘点计划才能审核");
            }
            
            if (approved) {
                // 审核通过，更新状态为已审核
                stocktaking.setStatus(3);
            } else {
                // 审核不通过，退回到进行中状态
                stocktaking.setStatus(1);
            }
            
            stocktaking.setAuditorId(SecurityUtils.getUserId());
            stocktaking.setAuditorName(username);
            stocktaking.setAuditTime(new Date());
            stocktaking.setRemark(auditRemark);
            stocktaking.setUpdateTime(new Date());
            stocktaking.setUpdateBy(username);
            
            stocktakingMapper.updateById(stocktaking);
            
            log.info("审核盘点成功，盘点单ID：{}，审核结果：{}", stocktakingId, approved ? "通过" : "不通过");
            return true;
        } catch (Exception e) {
            log.error("审核盘点失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("审核盘点失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyStocktakingDifferences(String stocktakingId, String username) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 3) {
                throw new RuntimeException("只有已审核的盘点计划才能应用差异");
            }
            
            // 查询有差异的明细
            LambdaQueryWrapper<ItemStocktakingDetail> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ItemStocktakingDetail::getStocktakingId, stocktakingId);
            queryWrapper.eq(ItemStocktakingDetail::getStatus, 2); // 有差异
            
            List<ItemStocktakingDetail> differenceDetails = detailMapper.selectList(queryWrapper);
            
            for (ItemStocktakingDetail detail : differenceDetails) {
                // 应用库存差异
                boolean success = itemService.updateItemStock(
                        detail.getItemId(),
                        detail.getWarehouseId(),
                        detail.getDifferenceQuantity().intValue(),
                        3, // 盘点操作类型
                        stocktakingId,
                        "盘点差异调整：" + detail.getDifferenceReason(),
                        detail.getShelfLocation()
                );
                
                if (!success) {
                    throw new RuntimeException("应用库存差异失败，物品ID：" + detail.getItemId());
                }
            }
            
            log.info("应用盘点差异成功，盘点单ID：{}，差异明细数量：{}", stocktakingId, differenceDetails.size());
            return true;
        } catch (Exception e) {
            log.error("应用盘点差异失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("应用盘点差异失败：" + e.getMessage());
        }
    }
    
    @Override
    public IPage<ItemStocktakingVo> selectStocktakingList(ItemStocktakingSearchRequest request) {
        Page<ItemStocktakingVo> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<ItemStocktakingVo> result = stocktakingMapper.selectStocktakingList(page, request);
        
        // 为每条记录设置仓库名称（通过数据字典获取）
        if (result.getRecords() != null) {
            for (ItemStocktakingVo stocktaking : result.getRecords()) {
                if (stocktaking.getWarehouseId() != null) {
                    stocktaking.setWarehouseName("仓库" + stocktaking.getWarehouseId());
                }
            }
        }
        
        return result;
    }
    
    @Override
    public ItemStocktakingVo getStocktakingDetail(String stocktakingId) {
        ItemStocktakingVo detail = stocktakingMapper.selectStocktakingDetail(stocktakingId);
        if (detail != null) {
            // 设置仓库名称（通过数据字典获取）
            if (detail.getWarehouseId() != null) {
                detail.setWarehouseName("仓库" + detail.getWarehouseId());
            }
            
            // 查询盘点明细
            List<ItemStocktakingDetailVo> details = detailMapper.selectStocktakingDetailList(stocktakingId);
            detail.setDetails(details);
            
            // 为明细也设置仓库名称
            if (details != null) {
                for (ItemStocktakingDetailVo detailVo : details) {
                    if (detailVo.getWarehouseId() != null) {
                        detailVo.setWarehouseName("仓库" + detailVo.getWarehouseId());
                    }
                }
            }
            
            // 查询盘点进度
            ItemStocktakingVo progress = detailMapper.selectStocktakingProgress(stocktakingId);
            if (progress != null) {
                detail.setTotalItems(progress.getTotalItems());
                detail.setCheckedItems(progress.getCheckedItems());
                detail.setDifferenceItems(progress.getDifferenceItems());
                detail.setProgress(progress.getProgress());
            }
        }
        return detail;
    }
    
    @Override
    public List<ItemStocktakingDetailVo> selectStocktakingDetailList(String stocktakingId) {
        List<ItemStocktakingDetailVo> details = detailMapper.selectStocktakingDetailList(stocktakingId);
        
        // 为每条明细设置仓库名称（通过数据字典获取）
        if (details != null) {
            for (ItemStocktakingDetailVo detail : details) {
                if (detail.getWarehouseId() != null) {
                    detail.setWarehouseName("仓库" + detail.getWarehouseId());
                }
            }
        }
        
        return details;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteStocktaking(String stocktakingId) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(stocktakingId);
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 0) {
                throw new RuntimeException("只有草稿状态的盘点计划才能删除");
            }
            
            // 删除明细
            LambdaQueryWrapper<ItemStocktakingDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(ItemStocktakingDetail::getStocktakingId, stocktakingId);
            detailMapper.delete(detailWrapper);
            
            // 删除主表
            stocktakingMapper.deleteById(stocktakingId);
            
            log.info("删除盘点计划成功，盘点单ID：{}", stocktakingId);
            return true;
        } catch (Exception e) {
            log.error("删除盘点计划失败，盘点单ID：{}", stocktakingId, e);
            throw new RuntimeException("删除盘点计划失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStocktaking(ItemStocktakingDto dto, String username) {
        try {
            ItemStocktaking stocktaking = stocktakingMapper.selectById(dto.getStocktakingId());
            if (stocktaking == null) {
                throw new RuntimeException("盘点计划不存在");
            }
            
            if (stocktaking.getStatus() != 0) {
                throw new RuntimeException("只有草稿状态的盘点计划才能修改");
            }
            
            // 更新盘点信息
            stocktaking.setStocktakingName(dto.getStocktakingName());
            stocktaking.setStocktakingType(dto.getStocktakingType());
            stocktaking.setWarehouseId(dto.getWarehouseId());
            stocktaking.setPlanStartTime(dto.getPlanStartTime());
            stocktaking.setPlanEndTime(dto.getPlanEndTime());
            stocktaking.setRemark(dto.getRemark());
            stocktaking.setUpdateTime(new Date());
            stocktaking.setUpdateBy(username);
            
            stocktakingMapper.updateById(stocktaking);
            
            log.info("更新盘点计划成功，盘点单ID：{}", dto.getStocktakingId());
            return true;
        } catch (Exception e) {
            log.error("更新盘点计划失败，盘点单ID：{}", dto.getStocktakingId(), e);
            throw new RuntimeException("更新盘点计划失败：" + e.getMessage());
        }
    }
    
    /**
     * 生成盘点单号
     */
    private String generateStocktakingCode() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String dateStr = sdf.format(new Date());
        
        // 查询当天最大序号
        String maxCode = stocktakingMapper.getNextStocktakingCode();
        int sequence = 1;
        
        if (maxCode != null && maxCode.startsWith("PD" + dateStr)) {
            String sequenceStr = maxCode.substring(10); // PD + 8位日期 = 10位
            sequence = Integer.parseInt(sequenceStr) + 1;
        }
        
        return String.format("PD%s%03d", dateStr, sequence);
    }
} 